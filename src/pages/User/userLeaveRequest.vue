<template>
  <UserNavigation />
  <div class="q-pa-md">
    <div class="row q-col-gutter-md">
      <!-- Left Column - Calendar -->
      <div class="col-12 col-md-7">
        <q-card flat class="calendar-container">
          <q-card-section>
            <!-- Month Navigation -->
            <div class="month-navigation">
              <q-btn flat round icon="chevron_left" @click="previousMonth" class="nav-btn" />
              <div class="month-title">{{ currentMonthDisplay }}</div>
              <q-btn flat round icon="chevron_right" @click="nextMonth" class="nav-btn" />
            </div>

            <!-- Calendar Grid -->
            <div class="calendar-grid">
              <!-- Day Headers -->
              <div class="day-headers">
                <div v-for="day in dayHeaders" :key="day" class="day-header">
                  {{ day }}
                </div>
              </div>

              <!-- Calendar Days -->
              <div class="calendar-days">
                <div
                  v-for="day in calendarDays"
                  :key="`${day.date}-${day.month}`"
                  :class="[
                    'calendar-day',
                    {
                      'other-month': day.isOtherMonth,
                      today: day.isToday,
                      selected: day.date === selectedDate,
                    },
                  ]"
                  @click="selectDate(day)"
                >
                  <span class="day-number">{{ day.date }}</span>
                  <div v-if="day.status" :class="['status-dot', `status-${day.status}`]"></div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- Right Column - Header and Actions -->
      <div class="col-12 col-md-5">
        <!-- Date Header -->
        <q-card flat class="date-header-card bg-mi">
          <q-card-section class="text-center">
            <q-icon name="edit" class="q-mr-sm" color="white" />
            <span class="date-header-text">{{ selectedDateDisplay }}</span>
            <q-btn
              flat
              round
              icon="close"
              color="white"
              class="float-right"
              @click="clearSelection"
            />
          </q-card-section>
        </q-card>

        <!-- Leave Statistics -->
        <q-card flat class="stats-card q-mt-md">
          <q-card-section>
            <div class="stats-title">จำนวนวันลาที่เหลือ</div>
            <div class="stats-row">
              <div class="stat-item">
                <div class="stat-label text-negative">ลากิจ</div>
                <div class="stat-value">{{ leaveStats.personal }}</div>
                <div class="stat-unit">วัน</div>
              </div>
              <div class="stat-divider"></div>
              <div class="stat-item">
                <div class="stat-label text-blue">ลาป่วย</div>
                <div class="stat-value">{{ leaveStats.sick }}</div>
                <div class="stat-unit">วัน</div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Action Buttons -->
        <div class="action-buttons q-mt-md">
          <q-btn class="action-btn schedule-btn bg-main" @click="openScheduleDialog" no-caps>
            <q-icon name="event_note" class="q-mr-sm" />
            ลงตารางงาน
          </q-btn>

          <q-btn class="action-btn leave-btn bg-blue q-mt-sm" @click="openLeaveDialog" no-caps>
            <q-icon name="event_busy" class="q-mr-sm" />
            ลงวันลา
          </q-btn>

          <q-btn
            class="action-btn holiday-btn bg-negative q-mt-sm"
            @click="openHolidayDialog"
            no-caps
          >
            <q-icon name="warning" class="q-mr-sm" />
            ลงวันหยุด
          </q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { date } from 'quasar'
import UserNavigation from 'src/components/userNavigation.vue'
import { useUserStore } from 'src/stores/userStore'
import { LeaveRequestService } from 'src/services/leaveRequestService'

// Store
const userStore = useUserStore()

// Reactive data
const currentDate = ref(new Date())
const selectedDate = ref<number | null>(8) // Default to 8th as shown in image
const leaveRequests = ref<any[]>([])
const loading = ref(false)

// Day headers for calendar
const dayHeaders = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su']

// Leave statistics
const leaveStats = ref({
  personal: 9,
  sick: 17,
})

// Computed properties
const currentMonthDisplay = computed(() => {
  const monthNames = [
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม',
  ]
  const year = currentDate.value.getFullYear() + 543 // Thai Buddhist year
  const month = monthNames[currentDate.value.getMonth()]
  return `${month} ${year}`
})

const selectedDateDisplay = computed(() => {
  if (!selectedDate.value) return ''
  const monthNames = [
    'มกราคม',
    'กุมภาพันธ์',
    'มีนาคม',
    'เมษายน',
    'พฤษภาคม',
    'มิถุนายน',
    'กรกฎาคม',
    'สิงหาคม',
    'กันยายน',
    'ตุลาคม',
    'พฤศจิกายน',
    'ธันวาคม',
  ]
  const year = currentDate.value.getFullYear() + 543
  const month = monthNames[currentDate.value.getMonth()]
  return `วันที่ ${selectedDate.value} ${month} ${year}`
})

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  // Get first day of month and calculate starting point
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)

  // Adjust to start from Monday (1 = Monday, 0 = Sunday)
  const dayOfWeek = (firstDay.getDay() + 6) % 7
  startDate.setDate(startDate.getDate() - dayOfWeek)

  const days = []
  const today = new Date()

  // Generate 42 days (6 weeks)
  for (let i = 0; i < 42; i++) {
    const currentDay = new Date(startDate)
    currentDay.setDate(startDate.getDate() + i)

    const isCurrentMonth = currentDay.getMonth() === month
    const isToday = date.isSameDate(currentDay, today, 'day')

    days.push({
      date: currentDay.getDate(),
      month: currentDay.getMonth(),
      year: currentDay.getFullYear(),
      isOtherMonth: !isCurrentMonth,
      isToday,
      status: getDateStatus(currentDay),
    })
  }

  return days
})

// Methods
const getDateStatus = (date: Date) => {
  // Mock data for demonstration - replace with actual leave request data
  const day = date.getDate()
  if ([1, 2, 3, 9, 15, 16, 21, 22, 23].includes(day)) return 'work'
  if ([5, 12, 13, 19, 20, 26, 27].includes(day)) return 'leave'
  if ([4, 10, 11, 17, 18, 24, 25].includes(day)) return 'holiday'
  if ([6, 7, 14].includes(day)) return 'present'
  return null
}

const selectDate = (day: any) => {
  if (day.isOtherMonth) return
  selectedDate.value = day.date
}

const clearSelection = () => {
  selectedDate.value = null
}

const previousMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  currentDate.value = newDate
}

const nextMonth = () => {
  const newDate = new Date(currentDate.value)
  newDate.setMonth(newDate.getMonth() + 1)
  currentDate.value = newDate
}

const openScheduleDialog = () => {
  console.log('Open schedule dialog')
}

const openLeaveDialog = () => {
  console.log('Open leave dialog')
}

const openHolidayDialog = () => {
  console.log('Open holiday dialog')
}

const fetchLeaveRequests = async () => {
  if (!userStore.currentUser?.id) return

  loading.value = true
  try {
    const requests = await LeaveRequestService.getUserLeaveRequests(userStore.currentUser.id)
    leaveRequests.value = requests
  } catch (error) {
    console.error('Error fetching leave requests:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await fetchLeaveRequests()
})
</script>

<style scoped>
/* Calendar Container */
.calendar-container {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Month Navigation */
.month-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 10px 0;
}

.month-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #294888;
}

.nav-btn {
  color: #294888;
}

.nav-btn:hover {
  background-color: rgba(41, 72, 136, 0.1);
}

/* Calendar Grid */
.calendar-grid {
  width: 100%;
}

.day-headers {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 10px;
}

.day-header {
  text-align: center;
  font-weight: bold;
  color: #666;
  padding: 8px;
  font-size: 0.9rem;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background-color: #f0f0f0;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 40px;
}

.calendar-day:hover {
  background-color: #f5f5f5;
}

.calendar-day.other-month {
  color: #ccc;
  background-color: #fafafa;
}

.calendar-day.today {
  background-color: #91d2c1;
  color: white;
  font-weight: bold;
}

.calendar-day.selected {
  background-color: #294888;
  color: white;
  font-weight: bold;
}

.day-number {
  font-size: 0.9rem;
  z-index: 1;
}

/* Status Dots */
.status-dot {
  position: absolute;
  bottom: 3px;
  right: 3px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-work {
  background-color: #439e62; /* Green for work days */
}

.status-leave {
  background-color: #b53638; /* Red for leave days */
}

.status-holiday {
  background-color: #b53638; /* Red for holidays */
}

.status-present {
  background-color: #439e62; /* Green for present */
}

/* Date Header Card */
.date-header-card {
  border-radius: 10px;
  color: white;
}

.date-header-text {
  font-size: 1.1rem;
  font-weight: bold;
}

/* Statistics Card */
.stats-card {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-title {
  text-align: center;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.stats-row {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-label {
  font-size: 0.9rem;
  margin-bottom: 5px;
  font-weight: 500;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 2px;
}

.stat-unit {
  font-size: 0.8rem;
  color: #666;
}

.stat-divider {
  width: 1px;
  height: 60px;
  background-color: #e0e0e0;
  margin: 0 15px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 100%;
  height: 60px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: bold;
  color: white;
  text-transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.schedule-btn {
  background-color: #91d2c1;
}

.leave-btn {
  background-color: #83a7d8;
}

.holiday-btn {
  background-color: #b53638;
}

/* Responsive Design */
@media (max-width: 768px) {
  .calendar-day {
    min-height: 35px;
  }

  .day-number {
    font-size: 0.8rem;
  }

  .status-dot {
    width: 6px;
    height: 6px;
    bottom: 2px;
    right: 2px;
  }

  .stat-value {
    font-size: 2rem;
  }

  .action-btn {
    height: 50px;
    font-size: 0.9rem;
  }

  .month-title {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .calendar-day {
    min-height: 30px;
  }

  .day-number {
    font-size: 0.7rem;
  }

  .stat-value {
    font-size: 1.8rem;
  }

  .action-btn {
    height: 45px;
    font-size: 0.85rem;
  }
}
</style>

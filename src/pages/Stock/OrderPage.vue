<template>
  <StockNavigation></StockNavigation>
  <div class="container q-pa-md q-ma-md">
    <div class="row wrap-container">
      <div class="col-8 col-md-12 col-sm-12 col-xs-12 search-container">
        <searchComponent v-model="searchQuery" placeholder="ค้นหา" />
      </div>
      <div class="col-4 filter-button-container">
        <filterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
        <AddPODialog></AddPODialog>
        <q-btn flat @click="openDialog" class="add-button q-mb-md" label="สั่งซื้อสินค้ารายการใหม่" />
      </div>
      <!-- <div class="col-2 text-right">
        <AddPODialog></AddPODialog>
        <q-btn
          flat
          @click="openDialog"
          class="add-button q-mb-md"
          label="สั่งซื้อสินค้ารายการใหม่"
        />
      </div> -->
    </div>
    <q-card flat class="custom-table">
      <q-table class="body-table" :rows="filteredOrders" :columns="columns" row-key="id" :pagination="pagination"
        :rows-per-page-options="[]" style="height: 100%; max-height: 700px">
        <template #body-cell-actions="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px">
            <q-btn icon="edit" padding="none" flat style="color: #e19f62" @click="handleOpenEdit(props.row)" />
            <q-btn icon="delete" padding="none" style="color: #b53638" flat
              @click="handleOpenDeleteDialog(props.row)" />
            <q-btn icon="info" padding="none" style="color: #294888" flat @click="handleOpenInfo(props.row)" />
          </q-td>
        </template>
        <template #body-cell-status="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px; text-align: center">
            <q-badge :label="props.row.status" :class="{
              'green-card': props.row.status === 'เสร็จสมบูรณ์',
              'blue-card': props.row.status === 'ดำเนินการ',
              'yellow-card': props.row.status === 'เตรียมรายการ',
              'red-card': props.row.status === 'ยกเลิก'
            }" style="
              height: 30px; 
              width: 100px; 
              display: flex; 
              align-items: center; 
              justify-content: center; 
              border-radius: 8px">
            </q-badge>
          </q-td>

        </template>
      </q-table>
      <InfoPOItemsDailog></InfoPOItemsDailog>
    </q-card>
    <ConfirmDeletePODialog v-model="formDelete" :item="selectedRow" @confirm="handleConfirmDelete" />
  </div>
  <PODetailsDialog></PODetailsDialog>
</template>

<script setup lang="ts">
import StockNavigation from 'src/components/StockNavigation.vue'
import filterComponent from 'src/components/filterComponent.vue'
import searchComponent from 'src/components/searchComponent.vue'
import { onMounted, ref, computed } from 'vue'
import { usePurchaseOrderStore } from 'src/stores/purchaseorder'
import { useDialogPO } from 'src/stores/dialog-po'
import AddPODialog from 'src/components/dialog/addPODialog.vue'
import type { QTableColumn } from 'quasar'
import ConfirmDeletePODialog from 'src/components/dialog/confirmDeletePODialog.vue'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import PODetailsDialog from 'src/components/dialog/PODetailsDialog.vue'
import { useDialogPODetails } from 'src/stores/dialog-po-details'
import InfoPOItemsDailog from 'src/components/dialog/InfoPOItemsDailog.vue'
// import type { PurchaseOrder } from 'src/types/purchaseOrder';
// import type { Product } from 'src/types/product';
const store = usePurchaseOrderStore()
const dialogPO = useDialogPO()
const dialogPODetails = useDialogPODetails()
const formDelete = ref(false)
const pagination = ref({
  rowsPerPage: 12,
})
const selectedRow = ref<PurchaseOrder>(JSON.parse(JSON.stringify(store.form)))

// const selectedRow = ref<PurchaseOrder>({
//   id: 0,
//   code: '',
//   supplier: {
//     id: 0,
//     supplier_number: '',
//     name: '',
//     address: '',
//     tel: '',
//     tax_number: '',
//     contact_name: '',
//     email: '',
//     fax: '',

//     type: {
//       id: 0,
//       name: '',
//     },
//   },
//   contact: '',
//   address: '',
//   date: new Date(),
//   user: {
//     id: 0,
//     code: '',
//     name: '',
//     tel: '',
//     role: '',
//     hour_work: 0,
//     sick_level: 0,
//     personal_leave: 0,
//     branch: {
//       id: 0,
//       name: '',
//       address: '',
//     },
//   },
//   po_total: 0,
//   tax: 0,
//   tax_total: 0,
//   status: '',
//   order_date: new Date(),
//   order_discount: 0,
//   note: '',
//   order_total: 0,
//   receive_total: 0,
//   product_price_tax: '',
//   order_discount_tax: '',
//   receive_status: '',
//   purchase_order_item: {
//     id: 0,
//     purchase_order: {
//       id: 0,
//       code: '',
//     } as PurchaseOrder,
//     product: {
//       id: 0,
//       product_code: '',
//       product_name: '',
//     } as Product,
//     quantity: 0,
//     unit_price: 0,
//     total_price: 0,
//   },
// })
onMounted(async () => {
  try {
    await store.fetchOrders()
  } catch (error) {
    console.error('Error fetching orders:', error)
  }
})
const columns = <QTableColumn[]>[
  {
    name: 'id',
    label: 'ลำดับ',
    field: 'id',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'code',
    label: 'รหัสใบสั่งซื้อ',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'supplier',
    label: 'ชื่อบริษัท',
    field: (row) => (row.supplier ? row.supplier.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'receive_total',
    label: 'จำนวนที่รับ',
    field: 'receive_total',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'order_total',
    label: 'จำนวนที่สั่ง',
    field: 'order_total',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'po_total',
    label: 'รวมเงิน',
    field: 'po_total',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'status',
    label: 'สถานะ',
    field: 'status',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
function handleOpenDeleteDialog(row: PurchaseOrder) {
  selectedRow.value = { ...row }
  formDelete.value = true
}
async function handleOpenEdit(row: PurchaseOrder) {
  store.form = { ...row }
  store.formItems = { ...row }
  await store.fetchOrdersItem() // เรียก API หลังจากเซ็ตค่า form.id
  dialogPODetails.open2('edit')
}

async function handleOpenInfo(row: PurchaseOrder) {
  store.form = { ...row }
  store.formItems = { ...row }
  await store.fetchOrdersItem() // เรียก API หลังจากเซ็ตค่า form.id
  dialogPODetails.openInfo('info')
}

const handleConfirmDelete = async (row: PurchaseOrder) => {
  try {
    await store.removeOrder(row.id)
  } catch (error) {
    console.error('Error removing product:', error)
  }
}
const openDialog = () => {
  dialogPO.open('')
  console.log('open add')
}
const selectedFilter = ref<string>('')

const filterOptions = [
  { label: 'รหัสสั่งซื้อสินค้า', value: 'code' },
  { label: 'ชื่อบริษัทจำหน่าย', value: 'name' },
  { label: 'รหัสบริษัทจำหน่าย', value: 'supplier_number' },
]
const searchQuery = ref('')

const filteredOrders = computed(() => {
  if (!searchQuery.value) {
    return store.orders
  }
  return store.orders.filter((PurchaseOrder) => {
    return (
      PurchaseOrder.supplier.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      PurchaseOrder.code.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      PurchaseOrder.supplier.supplier_number.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  })
})
</script>

<style scoped>
.green-card {
  background-color: #439E62;
  /* สีเขียว */
  color: white;
}

.yellow-card {
  background-color: #ED9B53;
  /* สีเหลือง */
  color: white;
}

.red-card {
  background-color: #B53638;
  /* สีแดง */
  color: white;
}

.blue-card {
  /* สีแดง */
  background-color: #83A7D8;
  color: white;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>

/* app global css */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Thai:wght@100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Thai+Looped:wght@100;200;300;400;500;600;700&display=swap');
* {
  font-family: 'IBM Plex Sans Thai Looped', sans-serif;
}

body {
  background-color: #f3f3f3;
}
.container {
  background-color: white;
  border-radius: 10px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  max-width: 300px;
  height: 40px;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

$primary: #294888;
$green: #439E62;
$bg: #91D2C1;
$positive: #36B54D;
$negative: #B53638;
$orange: #ED9B53;
$blue: #83A7D8;
$main: #609FA3;

// === Text Color Utilities ===
.text-primary { color: $primary !important; }
.text-green { color: $green !important; }
.text-bg { color: $bg !important; }
.text-positive { color: $positive !important; }
.text-negative { color: $negative !important; }
.text-orange { color: $orange !important; }
.text-blue { color: $blue !important; }
.text-main { color: $main !important; }

// === Background Color Utilities ===
.bg-primary { background-color: $primary !important; }
.bg-green { background-color: $green !important; }
.bg-bg { background-color: $bg !important; }
.bg-positive { background-color: $positive !important; }
.bg-negative { background-color: $negative !important; }
.bg-orange { background-color: $orange !important; }
.bg-blue { background-color: $blue !important; }
.bg-main { background-color: $main !important; }

// === Border Color Utilities ===
.border-primary { border-color: $primary !important; }
.border-green { border-color: $green !important; }
.border-bg { border-color: $bg !important; }
.border-positive { border-color: $positive !important; }
.border-negative { border-color: $negative !important; }
.border-orange { border-color: $orange !important; }
.border-blue { border-color: $blue !important; }
.border-main { border-color: $main !important; }

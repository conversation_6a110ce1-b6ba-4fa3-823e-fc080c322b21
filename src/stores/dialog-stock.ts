import { defineStore } from 'pinia'

export const useDialogStockStore = defineStore('dialog', {
  state: () => ({
    isOpen: false,
    isOpenInfo: false,
    mode: '',
  }),
  actions: {
    open(mode = 'edit') {
      this.isOpen = true
      this.mode = mode
    },
    openInfo(mode = 'info') {
      this.isOpenInfo = true
      this.mode = mode
    },
    close() {
      this.isOpen = false
    },
    closeInfo() {
      this.isOpenInfo = false
    },
  },
})

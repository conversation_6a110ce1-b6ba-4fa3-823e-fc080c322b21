import { defineStore } from 'pinia'

export const useDialogPODetails = defineStore('dialog2', {
  state: () => ({
    isOpen2: false,
    mode: '',
    isOpenProduct: false,
    isOpen3: false,
    isOpenInfo: false,
  }),
  actions: {
    open2(mode = 'add') {
      this.isOpen2 = true
      this.mode = mode
    },
    close2() {
      this.isOpen2 = false
    },
    openProduct(mode = 'add') {
      this.isOpenProduct = true
      this.mode = mode
    },
    closeProduct() {
      this.isOpenProduct = false
    },
    open3(mode = 'add') {
      this.isOpen3 = true
      this.mode = mode
    },
    close3() {
      this.isOpen3 = false
    },
    openInfo(mode = 'info') {
      this.isOpenInfo = true
      this.mode = mode
    },
    closeInfo() {
      this.isOpenInfo = false
    },
  },
})

<template>
  <q-dialog v-model="dialogPO.isOpen3">
    <q-card style="max-width: 1100px; width: 800px;">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">เพิ่ม ลด สินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh; margin-bottom: 10px;">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดการสั่งสินค้า
          </div>

          <div class="shadow-2 container">
            <div class="row">
              <!-- 🔹 ฝั่งซ้าย: ข้อมูลสินค้า -->
              <div class="col-6">
                <div class="row">
                  <div class="col-4 q-pr-md " style="margin-bottom: 5px; font-size: 12pt; margin-top: 5px;">ชื่อสินค้า:
                  </div>
                  <div class="col-8" style="font-size: 12pt; margin-top: 5px;">{{
                    orderStore.formOrderItems?.product.generic_name }}</div>
                </div>
                <div class="row">
                  <div class="col-4 q-pr-md" style="margin-bottom: 5px; font-size: 12pt;">รหัสสินค้า:</div>
                  <div class="col-8" style="font-size: 12pt;">{{ orderStore.formOrderItems?.product.product_code }}
                  </div>
                </div>
                <div class="row">
                  <div class="col-4 q-pr-md" style="margin-bottom: 20px; font-size: 12pt;">บริษัทจำหน่าย:</div>
                  <div class="col-8" style="font-size: 12pt;">{{ orderStore.formOrderItems?.product.distributor.name }}
                  </div>
                </div>


              </div>

              <!-- 🔹 ฝั่งขวา: สถานะสินค้า + คงเหลือ -->
              <div class="col-6 flex text-right" style="gap:20px;">
                <div class="status-container">
                  <div class="status-header">สถานะสินค้า</div>
                  <div class="status-body">{{ stockStore.formOrderItems.status }}</div>
                </div>
                <div class="status-container" style="margin-left: 10px;">
                  <div class="status-header">คงเหลือ</div>
                  <div class="status-body">{{ stockStore.formOrderItems.remaining }}</div>
                </div>
              </div>


            </div>
            <div class="row-table">
              <div class="col-3">ราคาต่อหน่วย</div>
              <div class="col-3">เพิ่ม-ลด สินค้า</div>
              <div class="col-3">รวม</div>
            </div>
            <q-list bordered class="order-list" style="max-height: 700px; overflow-y: auto;">
              <q-item v-model="orderStore.formOrderItems" :key="orderStore.formOrderItems.id" class="q-mb-sm">
                <q-item-section>
                  <div class="row-table-body">
                    <!-- 🔹 ราคาต่อหน่วย -->
                    <div class="col-3">
                      <q-input v-model="orderStore.formOrderItems.unit_price" type="number" class="quantity-input"
                        style="width: 70px;"
                        @update:model-value="(val) => orderStore.formOrderItems.unit_price = Number(val)" />
                    </div>

                    <!-- 🔹 ปุ่มเพิ่ม/ลดสินค้า -->
                    <div class="col-3 row items-center q-gutter-sm">
                      <div class="quantity-box">
                        <q-btn unelevated icon="remove" @click="decreaseQuantity(orderStore.formOrderItems)"
                          class="quantity-btn" />

                        <q-input v-model="orderStore.formOrderItems.quantity" type="number" borderless
                          class="quantity-input2" style="width: 50px;" @update:model-value="(value) => {
                            orderStore.formOrderItems.quantity = Number(value);
                            updateQuantity(orderStore.formOrderItems, orderStore.formOrderItems.quantity);
                          }" />

                        <q-btn unelevated icon="add" @click="increaseQuantity(orderStore.formOrderItems)"
                          class="quantity-btn2" />
                      </div>
                    </div>

                    <!-- 🔹 ราคา รวม -->
                    <div class="total-text">
                      {{ totalPrice }}
                    </div>
                  </div>
                </q-item-section>
              </q-item>
            </q-list>


          </div>
        </div>

      </q-card-section>
      <q-card-actions align="center" style="color: white;">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
        <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogPODetails } from "src/stores/dialog-po-details";
import { usePurchaseOrderStore } from "src/stores/purchaseorder";
import { useStockStore } from "src/stores/stock";
import type { PurchaseOrderItems } from "src/types/purchaseOrderitems";
import { computed, onMounted, watch } from "vue";
onMounted(async () => {
  console.log(stockStore.formOrderItems)
  await orderStore.fetchOrdersItem()
})
const dialogPO = useDialogPODetails();
const stockStore = useStockStore()
const orderStore = usePurchaseOrderStore()
watch(() => orderStore.formOrderItems.unit_price, (newValue) => {
  console.log("unit_price:", newValue, "Type:", typeof newValue);
});

const totalPrice = computed(() => {
  return (orderStore.formOrderItems.unit_price * orderStore.formOrderItems.quantity).toFixed(2);
});
const increaseQuantity = (row: PurchaseOrderItems) => {
  row.quantity++;
  row.total_price = (Number(row.unit_price) || 0) * row.quantity;
  // อัปเดต total_price ใน orderStore
  orderStore.formOrderItems.total_price = row.total_price;
  console.log(row.total_price)
};

const decreaseQuantity = (row: PurchaseOrderItems) => {
  if (row.quantity > 1) {
    row.quantity--;
    row.total_price = (Number(row.unit_price) || 0) * row.quantity;
  }
  // อัปเดต total_price ใน orderStore
  orderStore.formOrderItems.total_price = row.total_price;
  console.log(row.total_price)

};


const updateQuantity = (row: PurchaseOrderItems, quantity: number) => {
  row.quantity = quantity < 1 ? 1 : quantity;
  row.total_price = (Number(row.unit_price) || 0) * row.quantity;

  // ใช้การกระตุ้นให้ Vue อัปเดต state
  orderStore.formOrderItems = { ...orderStore.formOrderItems, total_price: row.total_price };
  console.log(row.total_price)

};

watch(
  () => [orderStore.formOrderItems.quantity, orderStore.formOrderItems.unit_price],
  () => {
    orderStore.formOrderItems.total_price = parseFloat(
      (orderStore.formOrderItems.unit_price * orderStore.formOrderItems.quantity).toFixed(2)
    );
  }
);

// const saveDialog = () => {
//   const copiedFormOrderItems: PurchaseOrderItems = JSON.parse(JSON.stringify(orderStore.formOrderItems));

//   if (dialogPO.mode === 'edit') {
//     const index = orderStore.orderItems.findIndex(item => item.product.id === copiedFormOrderItems.product.id);

//     if (index !== -1) {
//       orderStore.orderItems[index] = {
//         ...orderStore.orderItems[index], // ค่าที่แก้ไขไว้
//         ...copiedFormOrderItems
//       };
//     } else {
//       orderStore.orderItems.push(copiedFormOrderItems);
//     }

//     // ลบ product.id ออกจาก deletedIds ถ้ามีการเพิ่มกลับมา
//     orderStore.deletedIds = orderStore.deletedIds.filter(id => id !== copiedFormOrderItems.product.id);
//   } else {
//     if (!orderStore.deletedIds.includes(copiedFormOrderItems.product.id)) {
//       orderStore.editOrderItems = [...orderStore.editOrderItems, copiedFormOrderItems];
//     }
//   }

//   console.log("Deleted IDs after save:", orderStore.deletedIds);
//   closeDialog();
// };
const saveDialog = () => {
  const copiedFormOrderItems: PurchaseOrderItems = JSON.parse(JSON.stringify(orderStore.formOrderItems));

  if (dialogPO.mode === 'edit') {
    const index = orderStore.orderItems.findIndex(item => item.product.id === copiedFormOrderItems.product.id);

    if (index !== -1) {
      orderStore.orderItems[index] = {
        ...orderStore.orderItems[index],
        ...copiedFormOrderItems
      };
    } else {
      orderStore.orderItems.push(copiedFormOrderItems);
    }

    // ลบ product.id ออกจาก deletedIds ถ้ามี
    orderStore.deletedIds = orderStore.deletedIds.filter(id => id !== copiedFormOrderItems.product.id);

  } else {
    // กรณี add product ให้เข้า editOrderItems ได้เสมอ
    const exists = orderStore.editOrderItems.some(item => item.product.id === copiedFormOrderItems.product.id);
    if (!exists) {
      orderStore.editOrderItems.push(copiedFormOrderItems);
    }
  }

  console.log("Deleted IDs after save:", orderStore.deletedIds);
  closeDialog();
}

// const saveDialog = () => {
//   const copiedFormOrderItems: PurchaseOrderItems = JSON.parse(JSON.stringify(orderStore.formOrderItems));

//   if (dialogPO.mode === 'edit') {
//     const index = orderStore.orderItems.findIndex(item => item.product.id === copiedFormOrderItems.product.id);

//     if (index !== -1) {
//       orderStore.orderItems[index] = {
//         ...orderStore.orderItems[index], // ค่าที่แก้ไขไว้
//         ...copiedFormOrderItems
//       };
//     } else {
//       orderStore.orderItems.push(copiedFormOrderItems);
//     }

//     // ลบ product.id ออกจาก deletedIds ถ้ามีการเพิ่มกลับมา
//     orderStore.deletedIds = orderStore.deletedIds.filter(id => id !== copiedFormOrderItems.product.id);
//   } else {
//     if (!orderStore.deletedIds.includes(copiedFormOrderItems.product.id)) {
//       orderStore.editOrderItems = [...orderStore.editOrderItems, copiedFormOrderItems];
//     }
//   }

//   console.log("Deleted IDs after save:", orderStore.deletedIds);
//   closeDialog();
// };



const closeDialog = () => {
  if (dialogPO.mode === 'edit') {
    // หา item ที่เพิ่งบันทึก แล้วอัปเดตกลับเข้า formOrderItems
    const editedItem = orderStore.orderItems.find(item => item.product.id === orderStore.formOrderItems.product.id);
    if (editedItem) {
      Object.assign(orderStore.formOrderItems, editedItem);
    }
  } else {
    orderStore.resetFormOrderItems();
  }
  dialogPO.close3();
};
</script>


<style scoped>
:deep(.q-table thead tr) {
  font-size: 12pt;
  background-color: #83A7D8;
}

.row-table {
  align-items: center;
  height: 40px;
  background-color: #83A7D8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* กำหนด 3 คอลัมน์ที่มีขนาดเท่ากัน */
  align-items: center;
  text-align: center;
}

.row-table-body {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* กำหนด 3 คอลัมน์ที่มีขนาดเท่ากัน */
  align-items: center;
  text-align: center;
}

.col-3 {
  padding: 8px;
}

.row-table .col-3 {
  flex: 1;
  text-align: center;
}



.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

.body-table {
  background-color: #ffffff;
}

.status-container {
  flex-direction: column;
  align-items: center;
  height: 40px;
  width: 165px;
  text-align: center;
  font-family: sans-serif;
}

.status-header {
  background-color: #83A7D8;
  padding: 10px;
  color: white;
  height: 40px;
  width: 170px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.status-body {
  background-color: #fff;
  padding: 10px;
  height: 40px;
  width: 170px;

  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.header-box {
  background-color: #83A7D8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.showDetail-Box {
  background-color: #ffffff;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.header-box2 {
  background-color: #83A7D8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}

.quantity-box {
  display: flex;
  align-items: center;
  align-items: center;
  justify-content: center;
  border: 2px solid rgb(201, 201, 201);
  border-radius: 5px;
  padding: 3px;
  background-color: white;
  width: 150px;
  height: 40px;
  margin-left: 40px;
}

.quantity-btn {
  margin-top: 0%;
  margin-bottom: 0%;
  width: 50px;
  height: 35px;
  background-color: white !important;
  color: black !important;
  border-radius: 0%;
  border-color: grey;
  border-right: 2px solid #c9c9c9;
  align-items: center;
  justify-content: center;
}

.quantity-btn2 {
  /* margin-left: 10px;
  margin-right: 10px; */
  margin-top: 0%;
  margin-bottom: 0%;
  width: 50px;
  height: 35px;
  background-color: white !important;
  color: black !important;
  border-radius: 0%;
  border-left: 2px solid #c9c9c9;
  align-items: center;
  justify-content: center;
}

.quantity-input {
  margin-bottom: 0%;
  margin-top: 0%;
  margin-left: 55px;
  width: 50px;
  text-align: center;
  color: black;
  font-weight: bold;
  border: none;
  background: transparent;
}

.quantity-input2 {
  margin-bottom: 0%;
  margin-top: 0%;
  /* margin-left: 10px; */
  width: 50px;
  text-align: center;
  color: black;
  font-weight: bold;
  border: none;
  background: transparent;
}

.total-text {
  margin-left: 52px;
}

/* .quantity-btn,
.quantity-btn2 {
  min-width: 32px;
  height: 32px;
}

.quantity-input {
  text-align: center;
} */

.order-list {
  border-radius: 8px;
  /* padding: 8px; */
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  background: white;
}
</style>

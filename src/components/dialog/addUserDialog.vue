<template>
  <q-dialog v-model="dialogStore.isOpen">
    <q-card style="max-width: 800px; width: 100%">
      <q-card-section class="row items-center justify-between">
        <div class="text-h6 text-weight-bold">เพิ่มพนักงานใหม่</div>
        <q-btn icon="close" @click="closeDialog" flat rounded />
      </q-card-section>

      <q-separator />

      <q-card-section style="max-height: 80vh" class="scroll">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header">รายละเอียดพนักงาน</div>
          <div class="shadow-2 container">
            <div class="row">
              <div class="col-3">
                <div class="user-image-upload">
                  <div class="add-image-placeholder">
                    <img v-if="imagePreview" :src="imagePreview" class="preview-image" />
                    <div v-else class="plus-icon-container">
                      <q-icon name="add" size="60px" color="#4285F4" />
                    </div>
                  </div>
                  <q-file
                    v-model="userImage"
                    accept=".jpg, .png, .jpeg"
                    @update:model-value="onFileSelected"
                    class="hidden-input"
                  >
                    <q-btn
                      round
                      color="white"
                      text-color="grey"
                      icon="add"
                      class="upload-btn"
                      size="md"
                    />
                  </q-file>
                </div>
              </div>
              <div class="col-9">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-6">
                    <div class="row q-mb-md">
                      <div class="col-4 flex flex-center">รหัสพนักงาน</div>
                      <div class="col-8">
                        <q-input
                          class="input-container"
                          :model-value="form.id.toString()"
                          dense
                          borderless
                          readonly
                        />
                      </div>
                    </div>
                    <div class="row q-mb-md">
                      <div class="col-4 flex flex-center">ชื่อ-นามสกุล</div>
                      <div class="col-8">
                        <q-input class="input-container" v-model="form.name" dense borderless />
                      </div>
                    </div>
                    <div class="row q-mb-md">
                      <div class="col-4 flex flex-center">เบอร์โทร</div>
                      <div class="col-8">
                        <q-input
                          class="input-container"
                          v-model="form.tel"
                          dense
                          borderless
                          type="tel"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="col-12 col-md-6">
                    <div class="row q-mb-md">
                      <div class="col-4 flex flex-center">ตำแหน่ง</div>
                      <div class="col-8">
                        <div class="row">
                          <q-radio
                            v-model="form.role"
                            val="ประจำ"
                            label="พนักงานประจำ"
                            class="q-mr-md"
                          />
                          <q-radio v-model="form.role" val="พาร์ทไทม์" label="พนักงานพาร์ทไทม์" />
                        </div>
                      </div>
                    </div>
                    <div class="row q-mb-md">
                      <div class="col-4 flex flex-center">ประจำสาขา</div>
                      <div class="col-8">
                        <q-select
                          class="input-container"
                          v-model="form.branch"
                          :options="branchOptions"
                          dense
                          borderless
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="center" class="column">
        <div v-if="saveMessage" class="text-center q-mb-md">
          <q-chip
            :color="
              saveMessage.includes('สำเร็จ')
                ? 'positive'
                : saveMessage.includes('ผิดพลาด')
                  ? 'negative'
                  : 'info'
            "
            text-color="white"
            :icon="
              saveMessage.includes('สำเร็จ')
                ? 'check'
                : saveMessage.includes('ผิดพลาด')
                  ? 'error'
                  : 'info'
            "
          >
            {{ saveMessage }}
          </q-chip>
        </div>
        <q-btn
          class="btn-accept"
          label="บันทึก"
          @click="saveDialog"
          :disable="!isFormValid || saving"
          :loading="saving"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, reactive } from 'vue'
import { useUserStore } from 'src/stores/userStore'
import { useBranchStore } from 'src/stores/branch'
import { useUserDialogStore } from 'src/stores/dialog-user'

// Define the user form interface
interface UserForm {
  id: number
  name: string
  tel: string
  role: string
  branch: string
}

// Create stores
const userStore = useUserStore()
const branchStore = useBranchStore()
const dialogStore = useUserDialogStore()

// Create a local form state instead of using userStore.form
const form = reactive<UserForm>({
  id: 0,
  name: '',
  tel: '',
  role: '',
  branch: '',
})

const userImage = ref<File | null>(null)
const imagePreview = ref('')
const saving = ref(false)
const saveMessage = ref('')

// Get branch options from the branch store
const branchOptions = computed(() => branchStore.getBranchs.map((branch) => branch.label))

onMounted(async () => {
  await branchStore.fetchAllBranch()
  await userStore.fetchUsers() // Ensure users are loaded for ID generation
  resetForm()
})

const isFormValid = computed(() => {
  return !!form.name.trim() && !!form.tel.trim() && !!form.role && !!form.branch
})

const onFileSelected = (files: File) => {
  if (files) {
    const file = files
    const reader = new FileReader()
    reader.onload = (e) => {
      if (e.target) {
        imagePreview.value = e.target.result as string
      }
    }
    reader.readAsDataURL(file)
  }
}

const saveDialog = async () => {
  if (saving.value) return false

  try {
    saving.value = true
    saveMessage.value = 'กำลังบันทึกข้อมูล...'

    // Validate form
    if (!isFormValid.value) {
      console.error('Form is not valid')
      saveMessage.value = 'กรุณากรอกข้อมูลให้ครบถ้วน'
      return false
    }

    // Find the selected branch object
    const selectedBranch = branchStore.getBranchs.find((branch) => branch.label === form.branch)

    // Prepare user data
    const userData = {
      name: form.name.trim(),
      tel: form.tel.trim(),
      role: form.role,
      branch: selectedBranch
        ? {
            id: parseInt(selectedBranch.value),
            name: selectedBranch.label,
            address: '',
          }
        : { id: 1, name: 'บางแสน', address: '' },
    }

    // Add new user with image
    await userStore.addUser(userData, userImage.value || undefined)

    // Refresh user list to reflect new data
    await userStore.fetchUsers()

    saveMessage.value = 'บันทึกข้อมูลสำเร็จ!'

    // Close dialog and reset form after a short delay
    setTimeout(() => {
      closeDialog()
    }, 1000)

    return true
  } catch (error) {
    console.error('Error saving user:', error)
    saveMessage.value = 'เกิดข้อผิดพลาดในการบันทึกข้อมูล'
    return false
  } finally {
    setTimeout(() => {
      saving.value = false
      saveMessage.value = ''
    }, 1500)
  }
}

const closeDialog = () => {
  resetForm()
  dialogStore.close()
}

const generateEmployeeId = () => {
  // Generate new employee ID based on the highest existing ID
  const maxId = userStore.users.length > 0 ? Math.max(...userStore.users.map((u) => u.id)) : 0
  return maxId + 1
}

const resetForm = () => {
  form.id = generateEmployeeId()
  form.name = ''
  form.tel = ''
  form.role = 'ประจำ'
  form.branch = ''
  userImage.value = null
  imagePreview.value = ''
}
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding: 10px 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 45px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
  width: 100%;
}

.btn-accept {
  background-color: #36b54d;
  color: white;
  width: 120px;
}

.user-image-upload {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10px;
}

.upload-btn {
  position: absolute;
  bottom: 5px;
  right: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hidden-input {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.add-image-placeholder {
  width: 150px;
  height: 150px;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.plus-icon-container {
  width: 100%;
  height: 100%;
  background-color: #ffffff; /* Light blue color */
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>

<template>
  <q-list class="text-bold q-ml-xl q-mr-xl">
    <q-item v-for="item in links" :key="item.title" clickable v-ripple :to="item.path" active-class="active-link"
      class="rounded-item q-mb-md" @click="$emit('updateTitle', item.title)">
      <q-item-section avatar class="flex justify-center items-center">
        <q-icon :name="item.icon" />
      </q-item-section>

      <q-item-section>
        <q-item-label>{{ item.title }}</q-item-label>
        <q-item-label v-if="item.caption" caption>{{ item.caption }}</q-item-label>
      </q-item-section>
    </q-item>
  </q-list>
</template>

<script setup lang="ts">
export interface SubItemProps {
  title: string
  caption?: string
  path: string
  icon?: string
}

export interface EssentialLinkProps {
  title: string
  caption?: string
  path?: string
  icon?: string
  subItems?: SubItemProps[]
}

defineEmits(['updateTitle'])

const links: EssentialLinkProps[] = [
  { title: 'แดชบอร์ด', path: '/dashboard', icon: 'bar_chart' },
  { title: 'หน้าขาย', path: '/sales', icon: 'shopping_cart' },
  { title: 'สต็อกสินค้า', path: '/stock/inventory', icon: 'inventory_2' },
  { title: 'บริษัทจำหน่าย', path: '/suppliers', icon: 'apartment' },
  { title: 'พนักงาน', path: '/user/summary', icon: 'person' },
]
</script>

<style scoped>
.active-link {
  background: #5da5a2;
  color: white;
  border-radius: 25px;
}

.rounded-item {
  border-radius: 25px;
}

.rounded-item:hover {
  background: rgba(93, 165, 162, 0.2);
}

.rounded-item.active-link:hover {
  background: #5da5a2;
}
</style>